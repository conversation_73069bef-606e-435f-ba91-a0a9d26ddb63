/**
 * M3U8代理工具函数
 * 用于在Next.js环境中处理CORS问题
 */

/**
 * 检查是否需要使用代理
 * 在开发环境或者遇到CORS问题时使用代理
 */
export function shouldUseProxy(): boolean {
  // 在开发环境中总是使用代理
  if (process.env.NODE_ENV === 'development') {
    return true;
  }
  
  // 在生产环境中，如果是在浏览器扩展中运行，可能不需要代理
  // 这里可以根据实际情况调整
  return typeof window !== 'undefined' && window.location.hostname === 'localhost';
}

/**
 * 构建代理URL
 */
export function buildProxyUrl(originalUrl: string, headers?: Record<string, string>): string {
  const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
  const proxyUrl = `${baseUrl}/api/proxy/m3u8?url=${encodeURIComponent(originalUrl)}`;
  
  if (headers && Object.keys(headers).length > 0) {
    const headersParam = encodeURIComponent(JSON.stringify(headers));
    return `${proxyUrl}&headers=${headersParam}`;
  }
  
  return proxyUrl;
}

/**
 * 代理fetch请求
 * 自动处理CORS问题
 */
export async function proxyFetch(
  url: string, 
  options?: RequestInit
): Promise<Response> {
  // 提取请求头
  const headers = options?.headers ? 
    (options.headers instanceof Headers ? 
      Object.fromEntries(options.headers.entries()) : 
      options.headers as Record<string, string>
    ) : {};

  if (shouldUseProxy()) {
    // 使用代理
    const proxyUrl = buildProxyUrl(url, headers);
    console.log(`使用代理请求: ${url} -> ${proxyUrl}`);
    
    return fetch(proxyUrl, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
      }
    });
  } else {
    // 直接请求
    console.log(`直接请求: ${url}`);
    return fetch(url, options);
  }
}

/**
 * 专门用于M3U8文件的fetch
 */
export async function fetchM3u8(
  url: string,
  requestHeaders?: Array<{ name: string; value: string }>
): Promise<Response> {
  const headers = requestHeaders ? 
    Object.fromEntries(requestHeaders.map(h => [h.name, h.value])) : 
    {};

  // 添加M3U8特定的请求头
  const m3u8Headers = {
    'Accept': 'application/x-mpegURL, application/vnd.apple.mpegurl, application/json, text/plain',
    'Accept-Language': 'zh,zh-CN;q=0.9,en;q=0.8,es;q=0.7',
    ...headers
  };

  return proxyFetch(url, {
    method: 'GET',
    headers: m3u8Headers,
    mode: 'cors',
    credentials: 'omit'
  });
}

/**
 * 专门用于分片文件的fetch
 */
export async function fetchSegment(
  url: string,
  requestHeaders?: Array<{ name: string; value: string }>,
  onProgress?: (loaded: number, total: number) => void
): Promise<ArrayBuffer> {
  const headers = requestHeaders ? 
    Object.fromEntries(requestHeaders.map(h => [h.name, h.value])) : 
    {};

  const response = await proxyFetch(url, {
    method: 'GET',
    headers,
    mode: 'cors',
    credentials: 'omit'
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  const contentLength = response.headers.get('content-length');
  const total = contentLength ? parseInt(contentLength, 10) : 0;

  if (!response.body || !onProgress) {
    return response.arrayBuffer();
  }

  // 使用ReadableStream监控下载进度
  const reader = response.body.getReader();
  const chunks: Uint8Array[] = [];
  let loaded = 0;

  try {
    while (true) {
      const { done, value } = await reader.read();
      
      if (done) break;
      
      chunks.push(value);
      loaded += value.length;
      
      if (onProgress && total > 0) {
        onProgress(loaded, total);
      }
    }
  } finally {
    reader.releaseLock();
  }

  // 合并所有chunks
  const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
  const result = new Uint8Array(totalLength);
  let offset = 0;
  
  for (const chunk of chunks) {
    result.set(chunk, offset);
    offset += chunk.length;
  }

  return result.buffer;
}

/**
 * 获取分片大小（支持代理）
 */
export async function getSegmentSizeWithProxy(
  url: string,
  requestHeaders?: Array<{ name: string; value: string }>
): Promise<number> {
  try {
    const headers = requestHeaders ? 
      Object.fromEntries(requestHeaders.map(h => [h.name, h.value])) : 
      {};

    // 先尝试HEAD请求
    try {
      const headResponse = await proxyFetch(url, { 
        method: 'HEAD', 
        headers 
      });
      
      if (headResponse.ok) {
        const contentLength = headResponse.headers.get('content-length');
        if (contentLength) {
          return parseInt(contentLength, 10);
        }
      }
    } catch (error) {
      console.warn('HEAD请求失败，尝试Range请求:', error);
    }

    // 如果HEAD请求失败，使用Range请求
    const rangeResponse = await proxyFetch(url, {
      headers: {
        ...headers,
        'Range': 'bytes=0-0'
      }
    });

    if (rangeResponse.ok) {
      const contentRange = rangeResponse.headers.get('content-range');
      if (contentRange) {
        const match = contentRange.match(/bytes \d+-\d+\/(\d+)/);
        if (match) {
          return parseInt(match[1], 10);
        }
      }
    }

    return 0;
  } catch (error) {
    console.warn('获取分片大小失败:', error);
    return 0;
  }
}
