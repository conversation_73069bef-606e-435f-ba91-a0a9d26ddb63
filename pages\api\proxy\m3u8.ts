import { NextApiRequest, NextApiResponse } from 'next';

/**
 * Next.js API路由 - M3U8代理服务
 * 用于绕过CORS限制，代理m3u8文件和分片请求
 */
export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  // 只允许GET请求
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const { url, headers: clientHeaders } = req.query;

  // 验证URL参数
  if (!url || typeof url !== 'string') {
    return res.status(400).json({ error: 'Missing or invalid URL parameter' });
  }

  try {
    // 解析客户端传递的请求头
    let requestHeaders: Record<string, string> = {};
    
    if (clientHeaders && typeof clientHeaders === 'string') {
      try {
        requestHeaders = JSON.parse(clientHeaders);
      } catch (error) {
        console.warn('Failed to parse client headers:', error);
      }
    }

    // 构建代理请求头
    const proxyHeaders: Record<string, string> = {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
      'Accept': 'application/x-mpegURL, application/vnd.apple.mpegurl, application/json, text/plain, */*',
      'Accept-Language': 'zh,zh-CN;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      ...requestHeaders
    };

    // 根据URL设置合适的Origin和Referer
    const urlObj = new URL(url);
    if (urlObj.hostname.includes('cloudfront.net') || urlObj.hostname.includes('amazon')) {
      proxyHeaders['Origin'] = 'https://www.amazon.com';
      proxyHeaders['Referer'] = 'https://www.amazon.com/';
    }

    console.log(`代理请求: ${url}`);

    // 发起代理请求
    const response = await fetch(url, {
      method: 'GET',
      headers: proxyHeaders,
    });

    if (!response.ok) {
      console.error(`代理请求失败: ${response.status} ${response.statusText}`);
      return res.status(response.status).json({ 
        error: `Proxy request failed: ${response.status} ${response.statusText}` 
      });
    }

    // 获取响应内容类型
    const contentType = response.headers.get('content-type') || 'application/octet-stream';
    const contentLength = response.headers.get('content-length');

    // 设置响应头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', '*');
    res.setHeader('Content-Type', contentType);
    
    if (contentLength) {
      res.setHeader('Content-Length', contentLength);
    }

    // 如果是m3u8文件，需要处理内容中的相对URL
    if (contentType.includes('mpegurl') || contentType.includes('m3u8') || url.includes('.m3u8')) {
      const text = await response.text();
      const processedContent = processM3u8Content(text, url);
      return res.status(200).send(processedContent);
    }

    // 对于分片文件，直接流式传输
    const buffer = await response.arrayBuffer();
    return res.status(200).send(Buffer.from(buffer));

  } catch (error) {
    console.error('代理请求错误:', error);
    return res.status(500).json({ 
      error: 'Proxy request failed', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}

/**
 * 处理M3U8内容，将相对URL转换为代理URL
 */
function processM3u8Content(content: string, baseUrl: string): string {
  const lines = content.split('\n');
  const baseUrlObj = new URL(baseUrl);
  const basePath = baseUrlObj.pathname.substring(0, baseUrlObj.pathname.lastIndexOf('/') + 1);

  return lines.map(line => {
    const trimmedLine = line.trim();
    
    // 跳过注释行和空行
    if (trimmedLine.startsWith('#') || !trimmedLine) {
      return line;
    }

    // 如果是相对URL，转换为绝对URL，然后包装为代理URL
    if (!trimmedLine.startsWith('http')) {
      const absoluteUrl = new URL(trimmedLine, baseUrlObj.origin + basePath).href;
      return `/api/proxy/m3u8?url=${encodeURIComponent(absoluteUrl)}`;
    }

    // 如果是绝对URL，直接包装为代理URL
    return `/api/proxy/m3u8?url=${encodeURIComponent(trimmedLine)}`;
  }).join('\n');
}

// 配置API路由
export const config = {
  api: {
    responseLimit: '50mb', // 允许较大的响应体
  },
};
