import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    tailwindcss(),
    react(),
  ],
  server: {
    port: 3456,        // 设置固定端口为 3456
    strictPort: true,  // 如果端口被占用，不会自动尝试下一个端口，而是直接报错
    host: true,        // 允许外部访问（可选）
    open: true,        // 启动时自动打开浏览器（可选）
    proxy: {
      // 代理所有以 /api/proxy/ 开头的请求
      '/api/proxy': {
        target: '',
        changeOrigin: true,
        configure: (proxy, options) => {
          proxy.on('proxyReq', (proxyReq, req, res) => {
            // 从请求路径中提取真实的URL
            const realUrl = decodeURIComponent(req.url.replace('/api/proxy/', ''));
            console.log('代理请求:', realUrl);

            // 重写请求路径和主机
            const url = new URL(realUrl);
            proxyReq.path = url.pathname + url.search;
            proxyReq.setHeader('host', url.host);

            // 设置必要的请求头
            proxyReq.setHeader('origin', 'https://www.amazon.com');
            proxyReq.setHeader('referer', 'https://www.amazon.com/');
            proxyReq.setHeader('user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36');

            // 动态设置target
            options.target = `${url.protocol}//${url.host}`;
          });

          proxy.on('proxyRes', (proxyRes, req, res) => {
            // 添加CORS头
            proxyRes.headers['access-control-allow-origin'] = '*';
            proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
            proxyRes.headers['access-control-allow-headers'] = '*';
          });
        }
      }
    }
  }
})
